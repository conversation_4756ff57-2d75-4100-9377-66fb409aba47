# API接口使用说明

## 概述

本文档说明如何使用 `config/api.js` 中实现的所有API接口。所有接口都已按照小驼峰命名法实现，并提供了完整的错误处理和参数验证。

## 基础配置

```javascript
const api = require('../config/api.js')
```

### 配置信息
- **baseUrl**: `http://localhost:24556/wxapp/api`
- **超时时间**: 10秒
- **请求格式**: `application/json`
- **默认应用ID**: 1

## 接口列表

### 1. 检查打印机设备
检查打印机设备是否在库存中。

```javascript
// 使用设备名称检查
const result = await api.checkPrinterDevice({ name: 'SN123456' }, 'T50PRO')

// 使用设备ID检查
const result = await api.checkPrinterDevice({ deviceId: 'SN123456' }, 'T50PRO')

// 响应格式
{
  success: true,
  isInStock: true,
  deviceInfo: { /* 设备详细信息 */ },
  message: '查询成功'
}
```

### 2. 获取或添加打印机OpenId
获取微信用户的OpenId和手机号。

```javascript
const result = await api.getOrAddPrinterOpenId('wx_login_code')

// 响应格式
{
  success: true,
  openId: 'user_openid_string',
  phone: '13800138000',
  message: '获取成功'
}
```

### 3. 标签模板管理

#### 3.1 保存用户自定义标签模板
```javascript
const result = await api.addLabel('user_openid', '{"template": "json_content"}')

// 响应格式
{
  success: true,
  result: true,
  message: '保存成功'
}
```

#### 3.2 获取用户自定义标签模板列表
```javascript
const result = await api.getLabels('user_openid', 0, 10, 'id', 0)

// 参数说明：
// pageIndex: 页码（从0开始）
// pageSize: 页大小（0表示不分页，最大2500）
// orderFile: 排序字段，默认'id'
// sortType: 排序类型(0:正序 1:倒序)

// 响应格式
{
  success: true,
  total: 100,
  data: [/* 模板列表 */],
  message: '获取成功'
}
```

#### 3.3 删除用户自定义标签模板
```javascript
const result = await api.deleteLabel('user_openid', 123)

// 响应格式
{
  success: true,
  result: true,
  message: '删除成功'
}
```

### 4. 打印记录管理

#### 4.1 添加已打印标签记录
```javascript
const result = await api.addPrintedLabel('user_openid', '{"printed": "label_json"}')

// 响应格式
{
  success: true,
  result: true,
  message: '保存成功'
}
```

#### 4.2 获取用户已打印标签记录列表
```javascript
const result = await api.getPrintedLabels('user_openid', 0, 10, 'id', 1)

// 响应格式
{
  success: true,
  total: 50,
  data: [/* 打印记录列表 */],
  message: '获取成功'
}
```

#### 4.3 删除用户已打印标签记录
```javascript
const result = await api.deletePrintedLabel('user_openid', 456)

// 响应格式
{
  success: true,
  result: true,
  message: '删除成功'
}
```

### 5. 系统模板管理

#### 5.1 获取系统标签模板分类列表
```javascript
const result = await api.getSystemLabelTemplateCategories()

// 响应格式
{
  success: true,
  data: [
    {
      categoryId: 1,
      categoryName: '商品标签'
    }
  ],
  message: '获取成功'
}
```

#### 5.2 通过分类获取系统标签模板列表
```javascript
const result = await api.getSystemLabelTemplatesByCategory('商品标签', 0, 10)

// 参数说明：
// categoryName: 分类名称（可选，为空则获取全部系统模板）

// 响应格式
{
  success: true,
  total: 20,
  data: [
    {
      templateId: 1,
      templateName: '商品价格标签',
      templateContent: '{}',
      templateThumbnail: 'base64...',
      templateDescription: '用于商品价格展示',
      labelSize: '40x30mm',
      sceneTags: '商品,价格,零售'
    }
  ],
  message: '获取成功'
}
```

#### 5.3 搜索系统标签模板列表
```javascript
const searchParams = {
  templateName: '商品标签',    // 模板名称（可选，模糊搜索）
  labelSize: '40x30mm',       // 标签尺寸（可选，精确匹配）
  sceneTags: '商品,价格',     // 适用场景标签（可选，模糊搜索）
  categoryName: '商品标签'    // 分类名称（可选，精确匹配）
}

const result = await api.searchSystemLabelTemplates(searchParams, 0, 10)

// 响应格式同上
```

### 6. 打印记录统计

#### 6.1 添加打印记录
```javascript
const printData = {
  openId: 'user_openid',              // 微信用户OpenId（可选）
  printerSN: 'SN123456',              // 打印设备SN（必填）
  templateId: 1,                      // 打印模板ID（必填）
  printCount: 5,                      // 打印份数（必填）
  resultCode: 'SUCCESS',              // 打印结果编码（必填）
  resultDescription: '打印成功'        // 打印结果描述（必填）
}

const result = await api.addPrintRecord(printData)

// 响应格式
{
  success: true,
  result: true,
  message: '添加打印记录成功'
}
```

### 7. Excel文件解析

#### 7.1 解析Excel文件生成打印标签内容
```javascript
const result = await api.printLabelByExcel('/path/to/file.xlsx', 3, true)

// 参数说明：
// filePath: Excel文件路径
// templateLength: 模板长度（每个标签包含的字段数量）
// hasHeader: 是否包含标题栏，默认false

// 响应格式
{
  success: true,
  printLabels: [
    {
      label_contents: ['字段1', '字段2', '字段3']
    }
  ],
  message: '解析成功'
}
```

## 错误处理

所有接口都提供统一的错误处理格式：

```javascript
{
  success: false,
  message: '错误描述',
  code: 1000,
  originalMessage: '原始错误信息'
}
```

### 常见错误码
- `200`: 操作成功
- `1000`: 参数错误
- `2001`: 打印机设备不存在
- `2002`: 文件格式错误
- `3001`: 获取OpenId失败
- `401`: 鉴权失败
- `403`: 权限不足
- `999`: 系统繁忙，请稍后再试

## 使用示例

```javascript
// 在页面中使用
const api = require('../../config/api.js')

Page({
  async onLoad() {
    try {
      // 检查设备
      const deviceResult = await api.checkPrinterDevice({ name: 'SN123456' })
      if (deviceResult.success) {
        console.log('设备在库:', deviceResult.isInStock)
      }
      
      // 获取用户模板
      const templatesResult = await api.getLabels('user_openid', 0, 20)
      if (templatesResult.success) {
        this.setData({
          templates: templatesResult.data
        })
      }
    } catch (error) {
      console.error('API调用失败:', error)
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      })
    }
  }
})
```

## 注意事项

1. 所有接口都使用Promise，建议使用async/await语法
2. 接口参数使用小驼峰命名法（camelCase）
3. 分页参数PageIndex从0开始
4. Excel文件上传使用wx.uploadFile，其他接口使用wx.request
5. 建议在调用前进行参数验证，避免不必要的网络请求
6. 所有接口都有超时设置（10秒），请注意处理超时情况
