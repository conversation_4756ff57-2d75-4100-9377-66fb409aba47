/**
 * 标签模板配置文件
 * 直接存放原有格式的模板数据
 */

const defaultTemplates = [
  //存储标签1-001
  {
    "TemplateName": "存储标签1",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_001.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_001.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 17,
        "Y": 3,
        "Width": 33,
        "Height": 5,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 17,
        "Y": 12,
        "Width": 30,
        "Height": 5,
        "Content": "操作人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 17,
        "Y": 21,
        "Width": 33,
        "Height": 5,
        "Content": "日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      }
    ]
  },
  //存储标签2-002
  {
    "TemplateName": "存储标签2",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_002B.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_002B.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 21,
        "Y": 2,
        "Width": 33,
        "Height": 4,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 21,
        "Y": 9,
        "Width": 30,
        "Height": 4,
        "Content": "最高存储量",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 21,
        "Y": 16,
        "Width": 33,
        "Height": 4,
        "Content": "最低存储量",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 21,
        "Y": 23,
        "Width": 33,
        "Height": 4,
        "Content": "责任人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      }

    ]
  },
  //食品留样标签1-003
  {
    "TemplateName": "食品留样标签1",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_003.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_003.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 5.5,
        "Width": 33,
        "Height": 4,
        "Content": "留样餐次",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|早餐 |午餐 |晚餐"
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 11.5,
        "Width": 30,
        "Height": 4,
        "Content": "留样品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 17.5,
        "Width": 33,
        "Height": 4,
        "Content": "留样时间",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 23.5,
        "Width": 33,
        "Height": 4,
        "Content": "留样人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      }

    ]
  },
  //食品留样标签2-004
  {
    "TemplateName": "食品留样标签2",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_004.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_004.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 2,
        "Width": 33,
        "Height": 3.5,
        "Content": "餐次",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3.5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|早餐 |午餐 |晚餐"
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 6.5,
        "Width": 30,
        "Height": 3.5,
        "Content": "菜品",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3.5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
       {
        "AntiColor": false,
        "X": 15,
        "Y": 11,
        "Width": 30,
        "Height": 3.5,
        "Content": "重量",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3.5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 15.5,
        "Width": 33,
        "Height": 3.5,
        "Content": "时间",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3.5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE_TIME",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 20,
        "Width": 33,
        "Height": 3.5,
        "Content": "留样人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3.5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 24.5,
        "Width": 33,
        "Height": 3.5,
        "Content": "制作人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3.5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      }
    ]
  },
  //物料/区域标签1-005
  {
    "TemplateName": "物料/区域标签1",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/blank.png",
    "Thumbnail" : "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_005_Thumbnail.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 2,
        "Y": 6,
        "Width": 46,
        "Height": 12,
        "Content": "中文内容",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "12",
        "Format": "TEXT",
        "Orientation": 1,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 6
      },
      {
        "AntiColor": false,
        "X": 2,
        "Y": 20,
        "Width": 46,
        "Height": 4,
        "Content": "英文内容",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 1,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 30
      }
    ]
  },
  //物料/区域标签2-006
  {
    "TemplateName": "物料/区域标签2",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_006.png",
    "Thumbnail" : "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_006_Thumbnail.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_006.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 3,
        "Y": 2,
        "Width": 44,
        "Height": 24,
        "Content": "中文内容",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "11",
        "Format": "TEXT",
        "Orientation": 1,
        "AutoReturn": true,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      }
    ]
  },
  //效期表1-007
  {
    "TemplateName": "效期表1",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_007.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_007.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 5.5,
        "Width": 33,
        "Height": 4,
        "Content": "物料品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8,
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 11.5,
        "Width": 30,
        "Height": 4,
        "Content": "生产日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 17.5,
        "Width": 33,
        "Height": 4,
        "Content": "有效日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 16,
        "Y": 23.5,
        "Width": 33,
        "Height": 4,
        "Content": "贮存条件",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|常温 |冷藏 |冷冻"
      }
    ]
  },
  //效期表2-008
  {
    "TemplateName": "效期表2",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_008.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_008.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 5,
        "Width": 33,
        "Height": 3,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8,
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 10,
        "Width": 30,
        "Height": 3,
        "Content": "制作日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 15,
        "Width": 33,
        "Height": 3,
        "Content": "到期日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 20,
        "Width": 33,
        "Height": 3,
        "Content": "贮存条件",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|常温 |冷藏 |冷冻"
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 25,
        "Width": 33,
        "Height": 3,
        "Content": "制作人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      }
    ]
  },
  //效期表3-009
  {
    "TemplateName": "效期表3",
    "Width": 50,
    "Height": 30,
    "Rotate": 0,
    "Copies": 1,
    "Density": 2,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 25,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_009.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_009.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 4,
        "Width": 33,
        "Height": 5.5,
        "Content": "物料名称",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 8,
        "Width": 33,
        "Height": 5.5,
        "Content": "生产日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 12,
        "Width": 33,
        "Height": 5.5,
        "Content": "开封日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 16,
        "Width": 33,
        "Height": 5.5,
        "Content": "有效期至",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 20,
        "Width": 33,
        "Height": 5.5,
        "Content": "贮存条件",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|常温 |冷藏 |冷冻"
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 24,
        "Width": 33,
        "Height": 5.5,
        "Content": "制作人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
    ]
  },
  //效期表4-010
  {
    "TemplateName": "效期表4",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_010.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_010.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 17,
        "Y": 6,
        "Width": 33,
        "Height": 5,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 17,
        "Y": 16,
        "Width": 33,
        "Height": 5,
        "Content": "日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      }
    ]
  },
  //食材管控标签-011
  {
    "TemplateName": "食材管控标签",
    "Width": 50,
    "Height": 30,
    "Rotate": 0,
    "Copies": 1,
    "Density": 2,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 25,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_011.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_011.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 4,
        "Width": 33,
        "Height": 5.5,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 8,
        "Width": 33,
        "Height": 5.5,
        "Content": "生产日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 12,
        "Width": 33,
        "Height": 5.5,
        "Content": "入库日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 16,
        "Width": 33,
        "Height": 5.5,
        "Content": "有效期至",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 20,
        "Width": 33,
        "Height": 5.5,
        "Content": "贮存条件",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|常温 |冷藏 |冷冻"
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 24,
        "Width": 33,
        "Height": 5.5,
        "Content": "填写人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
    ]
  },
  //食品仓库标签-012
  {
    "TemplateName": "食品仓库标签",
    "Width": 50,
    "Height": 30,
    "Rotate": 0,
    "Copies": 1,
    "Density": 2,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 25,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_012.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_012.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 4,
        "Width": 33,
        "Height": 5.5,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 8,
        "Width": 33,
        "Height": 5.5,
        "Content": "购买日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 12,
        "Width": 33,
        "Height": 5.5,
        "Content": "保质期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 16,
        "Width": 33,
        "Height": 5.5,
        "Content": "贮存条件",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|常温 |冷藏 |冷冻"
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 20,
        "Width": 33,
        "Height": 5.5,
        "Content": "填写人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 24,
        "Width": 33,
        "Height": 5.5,
        "Content": "检验人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
    ]
  },
  //日期标签-013
  {
    "TemplateName": "日期标签",
    "Width": 50,
    "Height": 30,
    "Rotate": 0,
    "Copies": 1,
    "Density": 2,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 25,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/blank.png",
    "Thumbnail" : "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_013_Thumbnail.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 2,
        "Y": 12.5,
        "Width": 33,
        "Height": 4.5,
        "Content": "日期类型",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4.5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "OPTION",
        "InputRegex": "|日       期 |生产日期 |有效日期 |入库日期 |制作日期 |留样日期 |购买日期"
      },
      {
        "AntiColor": false,
        "X": 21,
        "Y": 12.5,
        "Width": 33,
        "Height": 4.5,
        "Content": ":",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4.5",
        "Format": "TEXT",
        "Orientation": 0,
        "InputFormat": "TEXT"
      },
      {
        "AntiColor": false,
        "X": 23,
        "Y": 12.5,
        "Width": 33,
        "Height": 4.5,
        "Content": "日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4.5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      }
    ]
  }
];

/**
 * ========== 远程系统模板服务（供页面调用） ==========
 */
const api = require('./api.js')

// 将系统模板记录转换为页面可用的模板对象
function mapSystemTemplateRecord(record = {}) {
  let content = {}
  try {
    if (typeof record.templateContent === 'string') {
      content = JSON.parse(record.templateContent || '{}')
    } else if (typeof record.templateContent === 'object' && record.templateContent) {
      content = record.templateContent
    }
  } catch (e) {
    console.warn('解析模板内容失败，使用空对象:', e)
    content = {}
  }

  const mapped = {
    ...content,
    TemplateName: record.templateName || content.TemplateName || '未命名模板',
    Thumbnail: record.templateThumbnail || content.Thumbnail || content.PreviewPath || '',
    TemplateId: record.templateId || content.TemplateId,
    LabelSize: record.labelSize || content.LabelSize,
    SceneTags: record.sceneTags || content.SceneTags,
    DrawObjects: Array.isArray(content.DrawObjects) ? content.DrawObjects : [],
    Width: content.Width || content.ImageWidth || 50,
    Height: content.Height || content.ImageHeight || 30,
  }
  return mapped
}

// 获取模板分类
async function getTemplateCategories() {
  const res = await api.getSystemLabelTemplateCategories()
  if (res && res.success) return res.data || []
  return []
}

// 搜索系统模板（分页）
async function loadTemplates(options = {}, pageIndex = 0, pageSize = 20) {
  const { categoryName, templateName, labelSize, sceneTags } = options
  const searchParams = {}
  if (templateName) searchParams.templateName = templateName
  if (labelSize) searchParams.labelSize = labelSize
  if (sceneTags) searchParams.sceneTags = sceneTags
  if (typeof categoryName !== 'undefined') searchParams.categoryName = categoryName
  const res = await api.searchSystemLabelTemplates(searchParams, pageIndex, pageSize)
  if (!res || !res.success) {
    throw new Error('searchSystemLabelTemplates failed')
  }
  const templates = (res.data || []).map(mapSystemTemplateRecord)
  return { templates, total: res.total || templates.length }
}

function getDefaultTemplates() {
  return JSON.parse(JSON.stringify(defaultTemplates))
}

module.exports = {
  defaultTemplates,
  getDefaultTemplates,
  getTemplateCategories,
  loadTemplates,
  mapSystemTemplateRecord
}



