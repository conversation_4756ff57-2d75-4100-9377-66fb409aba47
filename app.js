// app.js
const { getOrAddPrinterOpenId } = require('./config/api.js');

App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)
//一键开启、关闭log，只在版本为develop时打印log
//console.log = ()=> {};

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId
        if (res.code) {
          console.log('微信登录成功，获取code:', res.code);
          
          // 调用API获取OpenID
          getOrAddPrinterOpenId(res.code).then(result => {
            if (result.success && result.openId) {
              console.log('获取OpenID成功:', result.openId);
              
              // 缓存OpenID到本地存储
              wx.setStorageSync('openId', result.openId);
              
              // 如果有手机号，也一并缓存
              if (result.phone) {
                wx.setStorageSync('userPhone', result.phone);
              }
              
              // 将OpenID保存到全局数据中，方便在各页面使用
              this.globalData.openId = result.openId;
            } else {
              console.error('获取OpenID失败:', result.message || '未知错误');
            }
          }).catch(error => {
            console.error('请求OpenID接口失败:', error);
          });
        } else {
          console.error('微信登录失败:', res.errMsg);
        }
      },
      fail: err => {
        console.error('微信登录调用失败:', err);
      }
    })
  },
  globalData: {
    userInfo: null,
    openId: null
  }
})
