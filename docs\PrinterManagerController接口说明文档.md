# PrinterManagerController 接口说明文档

## 概述

PrinterManagerController 是打印机管理控制器，提供打印机设备检查、标签模板管理、打印记录管理等功能。

**基础信息：**
- 控制器路径：`api/PrinterManager/[action]`
- 响应格式：`application/json`
- 请求方式：所有接口均为 `POST` 请求

## 通用响应格式

所有接口都遵循统一的响应格式：

```json
{
  "Head": {
    "Code": "200",
    "Message": "操作成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    // 具体的响应数据
  }
}
```

**状态码说明：**
- `200`：操作成功
- `1000`：参数错误
- `2001`：打印机设备不存在
- `2002`：文件格式错误
- `999`：系统错误

## 接口列表

### 1. 检查打印机设备是否在库

**接口地址：** `POST /api/PrinterManager/CheckPrinterDevice`

**功能描述：** 根据打印机型号和SN码检查设备是否在库存中，并记录查询统计信息

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "Body": {
    "Model": "string",  // 打印机型号（必填，1-100字符）
    "SN": "string"      // 设备SN码（必填，1-100字符）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "查询成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "Model": "string",              // 打印机型号
    "SN": "string",                 // 设备SN码
    "IsInStock": true,              // 设备是否在库（true:在库 false:不在库）
    "FirstQueryTime": "2024-01-01T12:00:00", // 首次查询时间
    "LastQueryTime": "2024-01-01T12:00:00",  // 最后查询时间
    "QueryCount": 10,               // 查询次数
    "Remark": "string"              // 备注信息
  }
}
```

### 2. 解析Excel文件生成打印标签内容

**接口地址：** `POST /api/PrinterManager/PrintLabelByExcel`

**功能描述：** 上传Excel文件并按指定的模板长度解析生成标签内容

**请求参数：** `multipart/form-data`
- `file`：Excel文件（必填，支持.xls/.xlsx/.csv格式）
- `templateLength`：模板长度（必填，大于0的整数，表示每个标签包含的字段数量）
- `hasHeader`：是否包含标题栏（可选，默认false，true:跳过第一行 false:不跳过）

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "解析成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "print_labels": [
      {
        "label_contents": ["字段1", "字段2", "字段3"]  // 字符串数组，长度等于templateLength
      }
    ]
  }
}
```

### 3. 保存用户自定义标签模板

**接口地址：** `POST /api/PrinterManager/AddLabel`

**功能描述：** 保存用户自定义的标签模板到数据库

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "Body": {
    "OpenId": "string",  // 微信用户OpenId（必填）
    "Json": "string"     // 标签模板JSON内容（必填）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "保存成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": true  // 保存结果（true:成功 false:失败）
}
```

### 4. 获取用户自定义标签模板列表（分页）

**接口地址：** `POST /api/PrinterManager/GetLabels`

**功能描述：** 分页获取指定用户的自定义标签模板列表

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "PageIndex": 0,      // 页码（从0开始）
  "PageSize": 10,      // 页大小（0表示不分页，最大2500）
  "OrderFile": "id",   // 排序字段
  "SortType": 0,       // 排序类型(0:正序 1:倒序)
  "RequestParams": {
    "OpenId": "string" // 微信用户OpenId（必填）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "获取成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "Total": 100,  // 总记录数
    "Data": [      // 标签模板列表
      {
        // 标签模板详细信息
      }
    ]
  }
}
```

### 5. 删除用户自定义标签模板

**接口地址：** `POST /api/PrinterManager/DeleteLabel`

**功能描述：** 删除指定用户的自定义标签模板

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "Body": {
    "OpenId": "string",  // 微信用户OpenId（必填）
    "LabelId": 123       // 标签模板ID（必填）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "删除成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": true  // 删除结果（true:成功 false:失败）
}
```

### 6. 添加已打印标签记录

**接口地址：** `POST /api/PrinterManager/AddPrintedLabel`

**功能描述：** 保存用户已打印的标签记录到数据库，用于历史记录管理

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "Body": {
    "OpenId": "string",  // 微信用户OpenId（必填）
    "Json": "string"     // 已打印标签的JSON内容（必填）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "保存成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": true  // 保存结果（true:成功 false:失败）
}
```

### 7. 获取用户已打印标签记录列表（分页）

**接口地址：** `POST /api/PrinterManager/GetPrintedLabels`

**功能描述：** 分页获取指定用户的已打印标签记录列表

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "PageIndex": 0,      // 页码（从0开始）
  "PageSize": 10,      // 页大小（0表示不分页，最大2500）
  "OrderFile": "id",   // 排序字段
  "SortType": 0,       // 排序类型(0:正序 1:倒序)
  "RequestParams": {
    "OpenId": "string" // 微信用户OpenId（必填）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "获取成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "Total": 100,  // 总记录数
    "Data": [      // 已打印标签记录列表
      {
        // 打印历史详细信息
      }
    ]
  }
}
```

### 8. 删除用户已打印标签记录

**接口地址：** `POST /api/PrinterManager/DeletePrintedLabel`

**功能描述：** 删除指定用户的已打印标签记录

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "Body": {
    "OpenId": "string",  // 微信用户OpenId（必填）
    "LabelId": 123       // 已打印标签记录ID（必填）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "删除成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": true  // 删除结果（true:成功 false:失败）
}
```

### 9. 获取系统标签模板分类列表

**接口地址：** `POST /api/PrinterManager/GetSystemLabelTemplateCategories`

**功能描述：** 获取所有可用的系统标签模板分类信息

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "Body": {
    // 无需特殊参数，获取所有分类
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "获取成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": [
    {
      "CategoryId": 1,           // 分类ID
      "CategoryName": "商品标签" // 分类名称
    }
  ]
}
```

### 10. 通过分类获取系统标签模板列表（分页）

**接口地址：** `POST /api/PrinterManager/GetSystemLabelTemplatesByCategory`

**功能描述：** 根据分类名称分页获取系统标签模板列表

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "PageIndex": 0,      // 页码（从0开始）
  "PageSize": 10,      // 页大小（0表示不分页，最大2500）
  "OrderFile": "id",   // 排序字段
  "SortType": 0,       // 排序类型(0:正序 1:倒序)
  "RequestParams": {
    "CategoryName": "string" // 分类名称（可选，为空则获取全部系统模板）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "获取成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "Total": 100,  // 总记录数
    "Data": [      // 系统标签模板列表
      {
        "TemplateId": 1,                    // 模板ID
        "TemplateName": "商品价格标签",      // 模板名称
        "TemplateContent": "{}",            // 模板内容（JSON格式）
        "TemplateThumbnail": "base64...",   // 模板缩略图（Base64）
        "TemplateDescription": "用于商品价格展示", // 模板描述
        "LabelSize": "40x30mm",             // 标签尺寸
        "SceneTags": "商品,价格,零售"        // 适用场景标签
      }
    ]
  }
}
```

### 11. 搜索系统标签模板列表（分页）

**接口地址：** `POST /api/PrinterManager/SearchSystemLabelTemplates`

**功能描述：** 根据多个条件搜索系统标签模板

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "PageIndex": 0,      // 页码（从0开始）
  "PageSize": 10,      // 页大小（0表示不分页，最大2500）
  "OrderFile": "id",   // 排序字段
  "SortType": 0,       // 排序类型(0:正序 1:倒序)
  "RequestParams": {
    "TemplateName": "string",   // 模板名称（可选，模糊搜索）
    "LabelSize": "string",      // 标签尺寸（可选，精确匹配）
    "SceneTags": "string",      // 适用场景标签（可选，模糊搜索，支持多个关键词用逗号分隔）
    "CategoryName": "string"    // 分类名称（可选，精确匹配）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "搜索成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "Total": 50,   // 总记录数
    "Data": [      // 搜索结果列表
      {
        "TemplateId": 1,                    // 模板ID
        "TemplateName": "商品价格标签",      // 模板名称
        "TemplateContent": "{}",            // 模板内容（JSON格式）
        "TemplateThumbnail": "base64...",   // 模板缩略图（Base64）
        "TemplateDescription": "用于商品价格展示", // 模板描述
        "LabelSize": "40x30mm",             // 标签尺寸
        "SceneTags": "商品,价格,零售"        // 适用场景标签
      }
    ]
  }
}
```

### 12. 添加打印记录

**接口地址：** `POST /api/PrinterManager/AddPrintRecord`

**功能描述：** 记录打印机的打印操作，包括打印结果和统计信息

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "Body": {
    "OpenId": "string",              // 微信用户OpenId（可选）
    "PrinterSN": "string",           // 打印设备SN（必填，1-100字符）
    "TemplateId": 123,               // 打印模板ID（必填）
    "PrintCount": 5,                 // 打印份数（必填，大于0的整数）
    "ResultCode": "SUCCESS",         // 打印结果编码（必填）
    "ResultDescription": "打印成功"   // 打印结果描述（必填，最大500字符）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "添加打印记录成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": true  // 添加结果（true:成功 false:失败）
}
```

## 错误处理

所有接口在发生错误时都会返回相应的错误码和错误信息：

**常见错误响应：**
```json
{
  "Head": {
    "Code": "1000",
    "Message": "参数错误：打印机型号是必填项",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": null
}
```

## 注意事项

1. **分页参数**：PageIndex 从 0 开始，PageSize 为 0 时表示不分页
2. **文件上传**：Excel 文件上传接口使用 multipart/form-data 格式
3. **OpenId**：大部分接口需要微信用户的 OpenId 进行用户身份识别
4. **JSON 内容**：标签模板和打印记录的 JSON 内容格式需要符合系统规范
5. **字符长度限制**：注意各字段的长度限制，超出限制会返回参数错误
