/**
 * API配置文件
 * 管理所有API相关的配置和请求方法
 */

const apiConfig = {
  // ==================== 基础配置 ====================
  
  // API基础URL
  // baseUrl: 'http://localhost:24556/wxapp/api',
  baseUrl: 'https://adminfrontend.welshine.com/wxapp/api',
  
  // 默认请求头
  defaultHeaders: {
    'content-type': 'application/json'
  },
  
  // 默认超时时间（毫秒）
  timeout: 10000,
  
  // ==================== 接口地址 ====================

  endpoints: {
    // 检查打印机设备
    checkPrinterDevice: '/PrinterManager/CheckPrinterDevice',
    // 解析Excel文件生成打印标签内容
    printLabelByExcel: '/PrinterManager/PrintLabelByExcel',
    // 保存用户自定义标签模板
    addLabel: '/PrinterManager/AddLabel',
    // 获取用户自定义标签模板列表
    getLabels: '/PrinterManager/GetLabels',
    // 删除用户自定义标签模板
    deleteLabel: '/PrinterManager/DeleteLabel',
    // 添加已打印标签记录
    addPrintedLabel: '/PrinterManager/AddPrintedLabel',
    // 获取用户已打印标签记录列表
    getPrintedLabels: '/PrinterManager/GetPrintedLabels',
    // 删除用户已打印标签记录
    deletePrintedLabel: '/PrinterManager/DeletePrintedLabel',
    // 获取系统标签模板分类列表
    getSystemLabelTemplateCategories: '/PrinterManager/GetSystemLabelTemplateCategories',
    // 通过分类获取系统标签模板列表
    getSystemLabelTemplatesByCategory: '/PrinterManager/GetSystemLabelTemplatesByCategory',
    // 搜索系统标签模板列表
    searchSystemLabelTemplates: '/PrinterManager/SearchSystemLabelTemplates',
    // 添加打印记录
    addPrintRecord: '/PrinterManager/AddPrintRecord',
    // 获取或添加打印机OpenId
    getOrAddPrinterOpenId: '/User/GetOrAddPrinterOpenId'
  },
  
  // ==================== 默认参数 ====================

  // 默认应用ID
  defaultAppId: 1,

  // 默认设备型号
  defaultModel: 'T50PRO',

  // ==================== 错误码定义 ====================

  errorCodes: {
    // 系统级错误
    SERIALIZE_FAILED: -2,           // 序列化转换出错
    VALIDATION_FAILED: -1,         // 参数格式错误-特性校验
    NONE: 0,                       // None
    SUCCESS: 200,                  // 操作成功
    UNAUTHORIZED: 401,             // 鉴权失败
    FORBIDDEN: 403,                // 权限不足
    ERROR: 999,                    // 系统繁忙，请稍后再试
    PARAM: 1000,                   // 参数错误
    PRINTER_NOT_EXIST: 2001,       // 打印机设备不存在
    FILE_FORMAT_ERROR: 2002,       // 文件格式错误
    GET_OPENID_FAILED: 3001,       // 获取OpenId失败

    // 打印机相关错误
    PRINTER_NOT_EXIST_OLD: 9000,       // 设备不存在（旧版本）
    PRINTER_MODEL_NOT_EMPTY: 9001, // 打印机型号不能为空
    PRINTER_SN_NOT_EMPTY: 9002     // 设备SN码不能为空
  },

  // 错误消息映射
  errorMessages: {
    [-2]: '序列化转换出错',
    [-1]: '参数格式错误',
    [0]: 'None',
    [200]: '操作成功',
    [401]: '鉴权失败',
    [403]: '权限不足',
    [999]: '系统繁忙，请稍后再试',
    [1000]: '参数错误',
    [2001]: '打印机设备不存在',
    [2002]: '文件格式错误',
    [3001]: '获取OpenId失败',
    [9000]: '设备不存在',
    [9001]: '打印机型号不能为空',
    [9002]: '设备SN码不能为空'
  }
}

/**
 * 生成请求ID
 */
function generateRequestId() {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

/**
 * 获取用户ID
 */
function getUserId() {
  return wx.getStorageSync('uid') || 'anonymous_user'
}

/**
 * 构造标准请求头
 */
function buildRequestHead(customParams = {}) {
  return {
    requestId: generateRequestId(),
    uid: getUserId(),
    appId: apiConfig.defaultAppId,
    ...customParams
  }
}

/**
 * 获取错误消息
 * @param {string|number} code - 错误码
 * @returns {string} 错误消息
 */
function getErrorMessage(code) {
  const numCode = parseInt(code)
  return apiConfig.errorMessages[numCode] || `未知错误 (${code})`
}

/**
 * 检查打印机设备API
 * @param {Object} device - 设备信息
 * @param {string} device.name - 设备名称/序列号
 * @param {string} device.deviceId - 设备ID
 * @param {string} model - 设备型号，默认为T50PRO
 * @returns {Promise} 返回检查结果
 */
function checkPrinterDevice(device, model = apiConfig.defaultModel) {
  return new Promise((resolve, reject) => {
    // 参数验证
    const sn = device.name || device.deviceId
    if (!sn) {
      resolve({
        success: false,
        message: getErrorMessage(apiConfig.errorCodes.PRINTER_SN_NOT_EMPTY),
        code: apiConfig.errorCodes.PRINTER_SN_NOT_EMPTY
      })
      return
    }

    if (!model) {
      resolve({
        success: false,
        message: getErrorMessage(apiConfig.errorCodes.PRINTER_MODEL_NOT_EMPTY),
        code: apiConfig.errorCodes.PRINTER_MODEL_NOT_EMPTY
      })
      return
    }

    const requestData = {
      head: buildRequestHead(),
      body: {
        model: model,
        sn: sn
      }
    }

    console.log('检查打印机设备请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.checkPrinterDevice,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('设备检查响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            // 设备检查成功
            resolve({
              success: true,
              isInStock: res.data.result?.isInStock || false,
              deviceInfo: res.data.result,
              message: res.data.head?.message || '查询成功'
            })
          } else {
            // 根据错误码返回具体错误信息
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('设备检查请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 保存用户自定义标签模板API
 * @param {string} openId - 微信用户OpenId
 * @param {string} json - 标签模板JSON内容
 * @returns {Promise} 返回保存结果
 */
function addLabel(openId, json) {
  return new Promise((resolve, reject) => {
    // 参数验证
    if (!openId) {
      resolve({
        success: false,
        message: '微信用户OpenId不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    if (!json) {
      resolve({
        success: false,
        message: '标签模板JSON内容不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    const requestData = {
      head: buildRequestHead(),
      body: {
        openId: openId,
        json: json
      }
    }

    console.log('保存标签模板请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.addLabel,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('保存标签模板响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              result: res.data.result,
              message: res.data.head?.message || '保存成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('保存标签模板请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 获取用户自定义标签模板列表API
 * @param {string} openId - 微信用户OpenId
 * @param {number} pageIndex - 页码（从0开始）
 * @param {number} pageSize - 页大小（0表示不分页，最大2500）
 * @param {string} orderFile - 排序字段，默认为'id'
 * @param {number} sortType - 排序类型(0:正序 1:倒序)
 * @returns {Promise} 返回标签模板列表
 */
function getLabels(openId, pageIndex = 0, pageSize = 10, orderFile = 'id', sortType = 0) {
  return new Promise((resolve, reject) => {
    // 参数验证
    if (!openId) {
      resolve({
        success: false,
        message: '微信用户OpenId不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    const requestData = {
      head: buildRequestHead(),
      pageIndex: pageIndex,
      pageSize: pageSize,
      orderFile: orderFile,
      sortType: sortType,
      requestParams: {
        openId: openId
      }
    }

    console.log('获取标签模板列表请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.getLabels,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('获取标签模板列表响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              total: res.data.result?.total || 0,
              data: res.data.result?.data || [],
              message: res.data.head?.message || '获取成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('获取标签模板列表请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 删除用户自定义标签模板API
 * @param {string} openId - 微信用户OpenId
 * @param {number} labelId - 标签模板ID
 * @returns {Promise} 返回删除结果
 */
function deleteLabel(openId, labelId) {
  return new Promise((resolve, reject) => {
    // 参数验证
    if (!openId) {
      resolve({
        success: false,
        message: '微信用户OpenId不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    if (!labelId) {
      resolve({
        success: false,
        message: '标签模板ID不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    const requestData = {
      head: buildRequestHead(),
      body: {
        openId: openId,
        labelId: labelId
      }
    }

    console.log('删除标签模板请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.deleteLabel,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('删除标签模板响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              result: res.data.result,
              message: res.data.head?.message || '删除成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('删除标签模板请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 添加已打印标签记录API
 * @param {string} openId - 微信用户OpenId
 * @param {string} json - 已打印标签的JSON内容
 * @returns {Promise} 返回保存结果
 */
function addPrintedLabel(openId, json) {
  return new Promise((resolve, reject) => {
    // 参数验证
    if (!openId) {
      resolve({
        success: false,
        message: '微信用户OpenId不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    if (!json) {
      resolve({
        success: false,
        message: '已打印标签的JSON内容不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    const requestData = {
      head: buildRequestHead(),
      body: {
        openId: openId,
        json: json
      }
    }

    console.log('添加打印记录请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.addPrintedLabel,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('添加打印记录响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              result: res.data.result,
              message: res.data.head?.message || '保存成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('添加打印记录请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 获取用户已打印标签记录列表API
 * @param {string} openId - 微信用户OpenId
 * @param {number} pageIndex - 页码（从0开始）
 * @param {number} pageSize - 页大小（0表示不分页，最大2500）
 * @param {string} orderFile - 排序字段，默认为'id'
 * @param {number} sortType - 排序类型(0:正序 1:倒序)
 * @returns {Promise} 返回打印记录列表
 */
function getPrintedLabels(openId, pageIndex = 0, pageSize = 10, orderFile = 'id', sortType = 0) {
  return new Promise((resolve, reject) => {
    // 参数验证
    if (!openId) {
      resolve({
        success: false,
        message: '微信用户OpenId不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    const requestData = {
      head: buildRequestHead(),
      pageIndex: pageIndex,
      pageSize: pageSize,
      orderFile: orderFile,
      sortType: sortType,
      requestParams: {
        openId: openId
      }
    }

    console.log('获取打印记录列表请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.getPrintedLabels,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('获取打印记录列表响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              total: res.data.result?.total || 0,
              data: res.data.result?.data || [],
              message: res.data.head?.message || '获取成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('获取打印记录列表请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 删除用户已打印标签记录API
 * @param {string} openId - 微信用户OpenId
 * @param {number} labelId - 已打印标签记录ID
 * @returns {Promise} 返回删除结果
 */
function deletePrintedLabel(openId, labelId) {
  return new Promise((resolve, reject) => {
    // 参数验证
    if (!openId) {
      resolve({
        success: false,
        message: '微信用户OpenId不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    if (!labelId) {
      resolve({
        success: false,
        message: '已打印标签记录ID不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    const requestData = {
      head: buildRequestHead(),
      body: {
        openId: openId,
        labelId: labelId
      }
    }

    console.log('删除打印记录请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.deletePrintedLabel,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('删除打印记录响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              result: res.data.result,
              message: res.data.head?.message || '删除成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('删除打印记录请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 获取系统标签模板分类列表API
 * @returns {Promise} 返回分类列表
 */
function getSystemLabelTemplateCategories() {
  return new Promise((resolve, reject) => {
    const requestData = {
      head: buildRequestHead(),
      body: {}
    }

    console.log('获取系统模板分类请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.getSystemLabelTemplateCategories,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('获取系统模板分类响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              data: res.data.result || [],
              message: res.data.head?.message || '获取成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('获取系统模板分类请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 通过分类获取系统标签模板列表API
 * @param {string} categoryName - 分类名称（可选，为空则获取全部系统模板）
 * @param {number} pageIndex - 页码（从0开始）
 * @param {number} pageSize - 页大小（0表示不分页，最大2500）
 * @param {string} orderFile - 排序字段，默认为'id'
 * @param {number} sortType - 排序类型(0:正序 1:倒序)
 * @returns {Promise} 返回系统模板列表
 */
function getSystemLabelTemplatesByCategory(categoryName = '', pageIndex = 0, pageSize = 10, orderFile = 'id', sortType = 0) {
  return new Promise((resolve, reject) => {
    const requestData = {
      head: buildRequestHead(),
      pageIndex: pageIndex,
      pageSize: pageSize,
      orderFile: orderFile,
      sortType: sortType,
      requestParams: {
        categoryName: categoryName
      }
    }

    console.log('获取系统模板列表请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.getSystemLabelTemplatesByCategory,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('获取系统模板列表响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              total: res.data.result?.total || 0,
              data: res.data.result?.data || [],
              message: res.data.head?.message || '获取成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('获取系统模板列表请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 搜索系统标签模板列表API
 * @param {Object} searchParams - 搜索参数
 * @param {string} searchParams.templateName - 模板名称（可选，模糊搜索）
 * @param {string} searchParams.labelSize - 标签尺寸（可选，精确匹配）
 * @param {string} searchParams.sceneTags - 适用场景标签（可选，模糊搜索）
 * @param {string} searchParams.categoryName - 分类名称（可选，精确匹配）
 * @param {number} pageIndex - 页码（从0开始）
 * @param {number} pageSize - 页大小（0表示不分页，最大2500）
 * @param {string} orderFile - 排序字段，默认为'id'
 * @param {number} sortType - 排序类型(0:正序 1:倒序)
 * @returns {Promise} 返回搜索结果
 */
function searchSystemLabelTemplates(searchParams = {}, pageIndex = 0, pageSize = 10, orderFile = 'id', sortType = 0) {
  return new Promise((resolve, reject) => {
    const requestData = {
      head: buildRequestHead(),
      pageIndex: pageIndex,
      pageSize: pageSize,
      orderFile: orderFile,
      sortType: sortType,
      requestParams: {
        templateName: searchParams.templateName || '',
        labelSize: searchParams.labelSize || '',
        sceneTags: searchParams.sceneTags || '',
        categoryName: searchParams.categoryName || ''
      }
    }

    console.log('搜索系统模板请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.searchSystemLabelTemplates,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('搜索系统模板响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              total: res.data.result?.total || 0,
              data: res.data.result?.data || [],
              message: res.data.head?.message || '搜索成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('搜索系统模板请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 添加打印记录API
 * @param {Object} printData - 打印数据
 * @param {string} printData.openId - 微信用户OpenId（可选）
 * @param {string} printData.printerSN - 打印设备SN（必填）
 * @param {number} printData.templateId - 打印模板ID（必填）
 * @param {number} printData.printCount - 打印份数（必填）
 * @param {string} printData.resultCode - 打印结果编码（必填）
 * @param {string} printData.resultDescription - 打印结果描述（必填）
 * @returns {Promise} 返回添加结果
 */
function addPrintRecord(printData) {
  return new Promise((resolve, reject) => {
    // 参数验证
    if (!printData.printerSN) {
      resolve({
        success: false,
        message: '打印设备SN不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    if (!printData.templateId) {
      resolve({
        success: false,
        message: '打印模板ID不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    if (!printData.printCount || printData.printCount <= 0) {
      resolve({
        success: false,
        message: '打印份数必须大于0',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    if (!printData.resultCode) {
      resolve({
        success: false,
        message: '打印结果编码不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    if (!printData.resultDescription) {
      resolve({
        success: false,
        message: '打印结果描述不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    const requestData = {
      head: buildRequestHead(),
      body: {
        openId: printData.openId || '',
        printerSN: printData.printerSN,
        templateId: printData.templateId,
        printCount: printData.printCount,
        resultCode: printData.resultCode,
        resultDescription: printData.resultDescription
      }
    }

    console.log('添加打印记录请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.addPrintRecord,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('添加打印记录响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              result: res.data.result,
              message: res.data.head?.message || '添加打印记录成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('添加打印记录请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 获取或添加打印机OpenId API
 * @param {string} code - 微信登录code
 * @returns {Promise} 返回OpenId和手机号
 */
function getOrAddPrinterOpenId(code) {
  return new Promise((resolve, reject) => {
    // 参数验证
    if (!code) {
      resolve({
        success: false,
        message: '微信登录code不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    const requestData = {
      head: buildRequestHead(),
      body: {
        code: code
      }
    }

    console.log('获取OpenId请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.getOrAddPrinterOpenId,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('获取OpenId响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            resolve({
              success: true,
              openId: res.data.result?.openId || '',
              phone: res.data.result?.phone || '',
              message: res.data.head?.message || '获取成功'
            })
          } else {
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('获取OpenId请求失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 解析Excel文件生成打印标签内容API
 * @param {string} filePath - Excel文件路径
 * @param {number} templateLength - 模板长度（每个标签包含的字段数量）
 * @param {boolean} hasHeader - 是否包含标题栏，默认false
 * @returns {Promise} 返回解析结果
 */
function printLabelByExcel(filePath, templateLength, hasHeader = false) {
  return new Promise((resolve, reject) => {
    // 参数验证
    if (!filePath) {
      resolve({
        success: false,
        message: 'Excel文件路径不能为空',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    if (!templateLength || templateLength <= 0) {
      resolve({
        success: false,
        message: '模板长度必须大于0',
        code: apiConfig.errorCodes.PARAM
      })
      return
    }

    console.log('解析Excel文件请求:', { filePath, templateLength, hasHeader })

    wx.uploadFile({
      url: apiConfig.baseUrl + apiConfig.endpoints.printLabelByExcel,
      filePath: filePath,
      name: 'file',
      formData: {
        'templateLength': templateLength.toString(),
        'hasHeader': hasHeader.toString()
      },
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('解析Excel文件响应:', res)

        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data)
            const responseCode = data.head?.code

            if (responseCode === '200') {
              resolve({
                success: true,
                printLabels: data.result?.print_labels || [],
                message: data.head?.message || '解析成功'
              })
            } else {
              const errorCode = parseInt(responseCode)
              const errorMessage = getErrorMessage(errorCode)

              resolve({
                success: false,
                message: errorMessage,
                code: errorCode,
                originalMessage: data.head?.message
              })
            }
          } catch (parseError) {
            console.error('解析响应数据失败:', parseError)
            reject(new Error('解析响应数据失败'))
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('解析Excel文件请求失败:', error)
        reject(error)
      }
    })
  })
}

module.exports = {
  apiConfig,
  generateRequestId,
  getUserId,
  buildRequestHead,
  getErrorMessage,
  checkPrinterDevice,
  // printLabelByExcel,
  // addLabel,
  // getLabels,
  // deleteLabel,
  // addPrintedLabel,
  // getPrintedLabels,
  // deletePrintedLabel,
  getSystemLabelTemplateCategories,
  // getSystemLabelTemplatesByCategory,
  searchSystemLabelTemplates,
  addPrintRecord,
  getOrAddPrinterOpenId
}
